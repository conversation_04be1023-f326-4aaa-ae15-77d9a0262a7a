import React, { useState, useEffect } from 'react';
import { Icons } from '../constants/index';
import { dispatchService, DispatchRecord } from '../services/dispatchService';
import { getEmployees, apiClient } from '../services/api';
import { Employee } from '../types';

const DispatchCenterPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'planning' | 'approval' | 'manual' | 'smart'>('overview');
  const [dispatchRecords, setDispatchRecords] = useState<DispatchRecord[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [stations, setStations] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  // 调度审核相关状态
  const [selectedRecord, setSelectedRecord] = useState<DispatchRecord | null>(null);
  const [showApprovalModal, setShowApprovalModal] = useState(false);

  // 智能调度相关状态
  const [aiQuery, setAiQuery] = useState('');
  const [aiResults, setAiResults] = useState<Employee[]>([]);
  const [aiRecommendations, setAiRecommendations] = useState<Employee[]>([]);
  const [riskAnalysis, setRiskAnalysis] = useState('');
  const [showRiskModal, setShowRiskModal] = useState(false);

  // 新建调度相关状态
  const [searchFilters, setSearchFilters] = useState({
    position: '',
    department: '',
    station: '',
    experience: '',
    type: ''
  });
  const [searchResults, setSearchResults] = useState<Employee[]>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);

  useEffect(() => {
    const loadData = async () => {
      await Promise.all([
        fetchDispatchRecords(),
        fetchEmployees(),
        fetchStations()
      ]);
      setDataLoaded(true);
      console.log('🎉 调度中心所有数据加载完成');
    };
    loadData();
  }, []);

  const fetchStations = async () => {
    try {
      const data = await apiClient.request('/stations', { method: 'GET' });
      console.log('✅ 电站数据加载成功:', data?.length || 0);
      setStations(data || []);
    } catch (error) {
      console.error('获取电站数据失败:', error);
      setStations([]);
    }
  };

  // 调试：监听activeTab变化
  useEffect(() => {
    console.log('📋 调度中心当前activeTab:', activeTab);
  }, [activeTab]);

  const fetchDispatchRecords = async () => {
    try {
      const response = await dispatchService.getDispatchRecords();
      // 处理API返回的数据结构
      if (response && response.records) {
        setDispatchRecords(response.records);
      } else if (Array.isArray(response)) {
        setDispatchRecords(response);
      } else {
        setDispatchRecords([]);
      }
    } catch (error) {
      console.error('获取调度记录失败:', error);
      // 使用模拟数据作为后备
      setDispatchRecords([]);
    }
  };

  const fetchEmployees = async () => {
    try {
      const data = await getEmployees();
      console.log('✅ 调度中心员工数据加载成功:', data?.length || 0);
      setEmployees(data);
    } catch (error) {
      console.error('获取员工数据失败:', error);
      setEmployees([]);
    }
  };

  // 调度审核功能
  const handleApproveDispatch = async (recordId: string, approved: boolean, reason?: string) => {
    try {
      console.log('🔄 开始审核调度申请:', { recordId, approved, reason });
      await dispatchService.approveDispatch(recordId, { approved, reason });
      console.log('✅ 审核调度申请成功');
      fetchDispatchRecords();
      setShowApprovalModal(false);
      setSelectedRecord(null);
    } catch (error) {
      console.error('❌ 审核调度申请失败:', error);
    }
  };

  // AI查询功能
  const handleAiQuery = async () => {
    if (!aiQuery.trim()) return;
    
    setLoading(true);
    try {
      const data = await dispatchService.aiQuery({ query: aiQuery });
      setAiResults(data.employees);
      setRiskAnalysis(data.riskAnalysis);
      setShowRiskModal(true);
    } catch (error) {
      console.error('AI查询失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // AI智能推荐功能
  const handleAiRecommendation = async () => {
    if (!aiQuery.trim()) return;
    
    setLoading(true);
    try {
      const data = await dispatchService.aiRecommendation({ requirement: aiQuery });
      setAiRecommendations(data.recommendations);
      setRiskAnalysis(data.riskAnalysis);
      setShowRiskModal(true);
    } catch (error) {
      console.error('AI推荐失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 传统查询功能
  const handleTraditionalSearch = async () => {
    setLoading(true);
    try {
      // 这里应该调用员工搜索API，暂时使用现有员工数据进行过滤
      let filteredEmployees = employees;
      
      if (searchFilters.position) {
        filteredEmployees = filteredEmployees.filter(emp => 
          emp.position.includes(searchFilters.position)
        );
      }
      if (searchFilters.department) {
        filteredEmployees = filteredEmployees.filter(emp => 
          emp.department === searchFilters.department
        );
      }
      if (searchFilters.station) {
        filteredEmployees = filteredEmployees.filter(emp => 
          emp.currentStationId === searchFilters.station
        );
      }
      
      setSearchResults(filteredEmployees);
    } catch (error) {
      console.error('查询员工失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 批量创建调度申请
  const handleCreateBatchDispatch = async () => {
    if (selectedEmployees.length === 0) return;
    
    try {
      // 构建批量调度记录
      const records = selectedEmployees.map(employeeId => {
        const employee = employees.find(emp => emp.id === employeeId);
        return {
          employeeId,
          fromStationId: employee?.currentStationId || '',
          toStationId: '青海中控50MW', // 默认目标电站ID，这里应该是实际的电站ID
          dispatchType: 'temporary',
          startDate: new Date().toISOString().split('T')[0],
          endDate: undefined
        };
      });

      await dispatchService.createBatchDispatch({
        records,
        reason: '批量调度申请',
        source: 'manual'
      });
      
      setSelectedEmployees([]);
      setSearchResults([]);
      fetchDispatchRecords();
      alert('批量调度申请创建成功！');
    } catch (error) {
      console.error('创建批量调度申请失败:', error);
    }
  };

  // 确认AI推荐并创建调度申请
  const handleConfirmAiRecommendation = async (recommendation: any) => {
    try {
      // 获取员工的当前电站信息
      const employee = employees.find(emp => emp.id === recommendation.employeeId);
      const fromStation = employee?.currentStation?.name || '未知电站';
      
      await dispatchService.createDispatch({
        employeeId: recommendation.employeeId,
        fromStation: fromStation,
        toStation: recommendation.toStation,
        type: recommendation.type === 'ai_recommended' ? 'temporary' : (recommendation.type || 'temporary'),
        startDate: recommendation.startDate || new Date().toISOString().split('T')[0],
        reason: `AI推荐调度: ${recommendation.reason}`,
        source: 'ai_recommendation',
        aiQuery: aiQuery
      });
      
      setAiRecommendations([]);
      setShowRiskModal(false);
      fetchDispatchRecords();
      alert('AI推荐调度申请创建成功！');
    } catch (error) {
      console.error('创建AI推荐调度申请失败:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'completed': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'temporary': return '临时调度';
      case 'permanent': return '永久调度';
      case 'emergency': return '紧急调度';
      default: return type;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待审批';
      case 'approved': return '已审批';
      case 'rejected': return '已拒绝';
      case 'completed': return '已完成';
      default: return status;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">调度中心</h1>
        <div className="flex space-x-2" style={{zIndex: 10, position: 'relative'}}>
          <button
            onClick={() => {
              console.log('🔄 点击岗位配置标签');
              setActiveTab('overview');
              console.log('✅ 设置activeTab为overview');
            }}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'overview'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Icons.Dashboard className="w-4 h-4 inline mr-2" />
            岗位配置
          </button>
          <button
            onClick={() => {
              console.log('🔄 点击调度计划标签');
              setActiveTab('planning');
              console.log('✅ 设置activeTab为planning');
            }}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'planning'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Icons.Calendar className="w-4 h-4 inline mr-2" />
            调度计划
          </button>
          <button
            onClick={() => {
              console.log('🔄 点击申请审批标签');
              setActiveTab('approval');
              console.log('✅ 设置activeTab为approval');
            }}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'approval'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Icons.Eye className="w-4 h-4 inline mr-2" />
            申请审批
          </button>
          <button
            onClick={() => {
              console.log('🔄 点击手工调度标签');
              setActiveTab('manual');
              console.log('✅ 设置activeTab为manual');
            }}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'manual'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Icons.Plus className="w-4 h-4 inline mr-2" />
            手工调度
          </button>
          <button
            onClick={() => {
              console.log('🔄 点击智能调度标签');
              setActiveTab('smart');
              console.log('✅ 设置activeTab为smart');
            }}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'smart'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Icons.Sparkles className="w-4 h-4 inline mr-2" />
            智能调度
          </button>
        </div>
      </div>

      {/* 岗位配置页面 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">电站岗位配置概览</h2>
              <p className="text-gray-600 mt-1">查看各电站的岗位配置情况和人员分布</p>
            </div>
            <div className="p-6">
              {stations.length > 0 ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {stations.map(station => {
                      const stationEmployees = employees.filter(emp => {
                        const empStationId = typeof emp.currentStationId === 'string'
                          ? emp.currentStationId
                          : emp.currentStationId?.id || emp.currentStationId?._id;
                        const stationId = station.id || station._id;
                        return empStationId === stationId;
                      });

                      // 调试信息
                      if (station.name === '青海中控50MW') {
                        console.log('🔍 调试电站员工匹配 [浏览器:', navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Other', ']:', {
                          stationName: station.name,
                          stationId: station.id || station._id,
                          totalEmployees: employees.length,
                          matchedEmployees: stationEmployees.length,
                          dataLoaded: dataLoaded,
                          renderTime: new Date().toISOString(),
                          firstEmployee: employees[0] ? {
                            name: employees[0].name,
                            currentStationId: employees[0].currentStationId
                          } : null
                        });
                      }
                      return (
                        <div key={station.id} className="bg-gray-50 rounded-lg p-4">
                          <h3 className="font-medium text-gray-900 mb-2">{station.name}</h3>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">总人数:</span>
                              <span className="font-medium">{stationEmployees.length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">在岗:</span>
                              <span className="text-green-600">{stationEmployees.filter(emp => emp.status === 'active').length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">学习:</span>
                              <span className="text-blue-600">{stationEmployees.filter(emp => emp.status === 'learning').length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">外委:</span>
                              <span className="text-gray-600">{stationEmployees.filter(emp => emp.employeeType === 'external_contract').length}</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-4">系统概览</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-blue-600">{stations.length}</div>
                        <div className="text-sm text-gray-600">电站总数</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">{employees.length}</div>
                        <div className="text-sm text-gray-600">员工总数</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-orange-600">{employees.filter(emp => emp.status === 'active').length}</div>
                        <div className="text-sm text-gray-600">在岗人员</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-purple-600">{dispatchRecords.length}</div>
                        <div className="text-sm text-gray-600">调度记录</div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Icons.Dashboard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">正在加载数据...</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 调度计划页面 */}
      {activeTab === 'planning' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">调度计划管理</h2>
              <p className="text-gray-600 mt-1">制定按项目或按人员的调度计划</p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-3 flex items-center">
                    <Icons.Briefcase className="w-5 h-5 mr-2 text-blue-500" />
                    按项目制定计划
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">预先填写人员来源（自招/外委）、需要的岗位和人数</p>
                  <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                    新建项目计划
                  </button>
                </div>
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-3 flex items-center">
                    <Icons.Users className="w-5 h-5 mr-2 text-green-500" />
                    按人员制定计划
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">制定外调的时间起止时间和目标电站</p>
                  <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700">
                    新建人员计划
                  </button>
                </div>
              </div>
              <div className="mt-6">
                <h3 className="font-medium mb-3">现有计划</h3>
                <div className="text-center py-8">
                  <Icons.Calendar className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">暂无调度计划</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 申请审批页面 */}
      {activeTab === 'approval' && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold">调度申请审核</h2>
            <p className="text-gray-600 mt-1">审核待处理的调度申请</p>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">员工姓名</th>
                    <th className="text-left py-3 px-4">调度类型</th>
                    <th className="text-left py-3 px-4">从</th>
                    <th className="text-left py-3 px-4">到</th>
                    <th className="text-left py-3 px-4">申请日期</th>
                    <th className="text-left py-3 px-4">状态</th>
                    <th className="text-left py-3 px-4">来源</th>
                    <th className="text-left py-3 px-4">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {dispatchRecords.map((record) => (
                    <tr key={record.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium">{record.employeeName}</td>
                      <td className="py-3 px-4">{getTypeText(record.type)}</td>
                      <td className="py-3 px-4">{record.fromStation}</td>
                      <td className="py-3 px-4">{record.toStation}</td>
                      <td className="py-3 px-4">{record.requestDate}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                          {getStatusText(record.status)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        {record.source === 'ai_query' && <Icons.Search className="w-4 h-4 text-blue-500" />}
                        {record.source === 'ai_recommendation' && <Icons.Sparkles className="w-4 h-4 text-purple-500" />}
                        {record.source === 'manual' && <Icons.UserCircle className="w-4 h-4 text-gray-500" />}
                      </td>
                      <td className="py-3 px-4">
                        {record.status === 'pending' && (
                          <button
                            onClick={() => {
                              console.log('🔍 点击审核按钮:', record);
                              setSelectedRecord(record);
                              setShowApprovalModal(true);
                              console.log('✅ 设置审核模态框显示状态为 true');
                            }}
                            className="text-blue-600 hover:text-blue-800 font-medium"
                          >
                            审核
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* 智能调度页面 */}
      {activeTab === 'smart' && (
        <div className="space-y-6">
          {/* 智能分析概览 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">调度风险评估</h3>
                  <p className="text-blue-100 mt-1">AI分析当前调度风险</p>
                </div>
                <Icons.AlertTriangle className="w-8 h-8 text-blue-200" />
              </div>
              <div className="mt-4">
                <div className="text-2xl font-bold">低风险</div>
                <div className="text-sm text-blue-200">基于历史数据和当前状态</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">优化建议</h3>
                  <p className="text-green-100 mt-1">AI生成的调度优化方案</p>
                </div>
                <Icons.Sparkles className="w-8 h-8 text-green-200" />
              </div>
              <div className="mt-4">
                <div className="text-2xl font-bold">3条建议</div>
                <div className="text-sm text-green-200">可提升15%效率</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">预测分析</h3>
                  <p className="text-purple-100 mt-1">未来7天调度预测</p>
                </div>
                <Icons.TrendingUp className="w-8 h-8 text-purple-200" />
              </div>
              <div className="mt-4">
                <div className="text-2xl font-bold">稳定</div>
                <div className="text-sm text-purple-200">人员需求平衡</div>
              </div>
            </div>
          </div>

          {/* AI对话助手 */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold flex items-center">
                <Icons.MessageSquare className="w-5 h-5 mr-2 text-blue-500" />
                AI调度助手
              </h2>
              <p className="text-gray-600 mt-1">与AI助手对话，获取智能调度建议、风险分析和优化方案</p>
            </div>
            <div className="p-6">
              {/* 对话历史 */}
              <div className="h-64 overflow-y-auto border rounded-lg p-4 mb-4 bg-gray-50">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <Icons.Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <p className="text-sm">您好！我是AI调度助手。我可以帮您：</p>
                      <ul className="text-sm text-gray-600 mt-2 space-y-1">
                        <li>• 分析调度风险和冲突</li>
                        <li>• 推荐最优调度方案</li>
                        <li>• 预测人员需求趋势</li>
                        <li>• 优化调度效率</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* 输入区域 */}
              <div className="flex space-x-3">
                <input
                  type="text"
                  value={aiQuery}
                  onChange={(e) => setAiQuery(e.target.value)}
                  placeholder="请描述您的调度问题或需求..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  style={{zIndex: 1, position: 'relative', pointerEvents: 'auto'}}
                />
                <button
                  onClick={handleAiQuery}
                  disabled={loading || !aiQuery.trim()}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icons.Send className="w-4 h-4 mr-2" />
                  {loading ? '分析中...' : '发送'}
                </button>
                  <button
                    onClick={handleAiRecommendation}
                    disabled={loading || !aiQuery.trim()}
                    className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Icons.Sparkles className="w-4 h-4 mr-2" />
                    {loading ? '推荐中...' : 'AI推荐'}
                  </button>
                </div>
              </div>
            </div>

          {/* AI查询结果 */}
          {aiResults.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold">AI查询结果</h3>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {aiResults.map((employee) => (
                    <div key={employee.id} className="border rounded-lg p-4">
                      <h4 className="font-medium">{employee.name}</h4>
                      <p className="text-sm text-gray-600">{employee.position}</p>
                      <p className="text-sm text-gray-600">{employee.currentStation?.name || '未分配'}</p>
                      <p className="text-sm text-gray-600">{employee.experience}年经验</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* AI推荐结果 */}
          {aiRecommendations.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold">AI智能推荐</h3>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {aiRecommendations.map((employee) => (
                    <div key={employee.id} className="border rounded-lg p-4">
                      <h4 className="font-medium">{employee.name}</h4>
                      <p className="text-sm text-gray-600">{employee.position}</p>
                      <p className="text-sm text-gray-600">{employee.currentStation?.name || '未分配'}</p>
                      <p className="text-sm text-gray-600">{employee.experience}年经验</p>
                      <button
                        onClick={() => handleConfirmAiRecommendation({
                          employeeId: employee.id,
                          toStation: 'S001', // 默认目标电站
                          type: 'temporary',
                          reason: '基于AI推荐的调度'
                        })}
                        className="mt-2 w-full px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700"
                      >
                        确认推荐
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 手工调度页面 */}
      {activeTab === 'manual' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">手工调度</h2>
              <p className="text-gray-600 mt-1">按照查询条件，查询相关人员或岗位进行批量或个人调度</p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">职位</label>
                  <select
                    value={searchFilters.position}
                    onChange={(e) => setSearchFilters({...searchFilters, position: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">全部职位</option>
                    <option value="项目经理">项目经理</option>
                    <option value="电气专工">电气专工</option>
                    <option value="值长">值长</option>
                    <option value="安全员">安全员</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">部门</label>
                  <select
                    value={searchFilters.department}
                    onChange={(e) => setSearchFilters({...searchFilters, department: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">全部部门</option>
                    <option value="management">管理部</option>
                    <option value="production">生产部</option>
                    <option value="equipment">设备部</option>
                    <option value="safety">安全部</option>
                    <option value="maintenance">维护部</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">电站</label>
                  <select
                    value={searchFilters.station}
                    onChange={(e) => setSearchFilters({...searchFilters, station: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">全部电站</option>
                    {stations.map(station => (
                      <option key={station.id} value={station.name}>{station.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">经验年限</label>
                  <select
                    value={searchFilters.experience}
                    onChange={(e) => setSearchFilters({...searchFilters, experience: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    style={{zIndex: 1, position: 'relative', pointerEvents: 'auto'}}
                  >
                    <option value="">不限</option>
                    <option value="1-3">1-3年</option>
                    <option value="3-5">3-5年</option>
                    <option value="5+">5年以上</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">员工类型</label>
                  <select
                    value={searchFilters.type}
                    onChange={(e) => setSearchFilters({...searchFilters, type: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">全部类型</option>
                    <option value="regular">正式员工</option>
                    <option value="intern">实习生</option>
                    <option value="contractor">外包员工</option>
                  </select>
                </div>
              </div>
              <button
                onClick={handleTraditionalSearch}
                disabled={loading}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                <Icons.Search className="w-4 h-4 mr-2" />
                {loading ? '查询中...' : '查询员工'}
              </button>
            </div>
          </div>

          {/* 调度设置 */}
          {searchResults.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold mb-4">调度设置</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">目标电站</label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                      <option value="">请选择目标电站</option>
                      {stations.map(station => (
                        <option key={station.id} value={station.id}>{station.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                    <input
                      type="datetime-local"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                    <input
                      type="datetime-local"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 查询结果 */}
          {searchResults.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-lg font-semibold">查询结果 ({searchResults.length}人)</h3>
                {selectedEmployees.length > 0 && (
                  <button
                    onClick={handleCreateBatchDispatch}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <Icons.Plus className="w-4 h-4 mr-2" />
                    批量创建调度申请 ({selectedEmployees.length})
                  </button>
                )}
              </div>
              <div className="p-6">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">
                          <input
                            type="checkbox"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedEmployees(searchResults.map(emp => emp.id));
                              } else {
                                setSelectedEmployees([]);
                              }
                            }}
                            checked={selectedEmployees.length === searchResults.length}
                          />
                        </th>
                        <th className="text-left py-3 px-4">姓名</th>
                        <th className="text-left py-3 px-4">职位</th>
                        <th className="text-left py-3 px-4">部门</th>
                        <th className="text-left py-3 px-4">电站</th>
                        <th className="text-left py-3 px-4">经验</th>
                        <th className="text-left py-3 px-4">类型</th>
                      </tr>
                    </thead>
                    <tbody>
                      {searchResults.map((employee) => (
                        <tr key={employee.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <input
                              type="checkbox"
                              checked={selectedEmployees.includes(employee.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedEmployees([...selectedEmployees, employee.id]);
                                } else {
                                  setSelectedEmployees(selectedEmployees.filter(id => id !== employee.id));
                                }
                              }}
                            />
                          </td>
                          <td className="py-3 px-4 font-medium">{employee.name}</td>
                          <td className="py-3 px-4">{employee.position}</td>
                          <td className="py-3 px-4">{employee.department}</td>
                          <td className="py-3 px-4">{employee.currentStation?.name || '未分配'}</td>
                          <td className="py-3 px-4">{employee.experience || 0}年</td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              employee.status === 'active' || employee.status === '在职' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {employee.status === 'active' ? '在职' : employee.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 审核模态框 */}
      {showApprovalModal && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">审核调度申请</h3>
            <div className="space-y-3 mb-6">
              <p><strong>员工:</strong> {selectedRecord.employeeName}</p>
              <p><strong>类型:</strong> {getTypeText(selectedRecord.type)}</p>
              <p><strong>从:</strong> {selectedRecord.fromStation}</p>
              <p><strong>到:</strong> {selectedRecord.toStation}</p>
              <p><strong>原因:</strong> {selectedRecord.reason}</p>
              {selectedRecord.aiQuery && (
                <p><strong>AI查询:</strong> {selectedRecord.aiQuery}</p>
              )}
              {selectedRecord.riskAnalysis && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm"><strong>风险分析:</strong></p>
                  <p className="text-sm text-yellow-800">{selectedRecord.riskAnalysis}</p>
                </div>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => handleApproveDispatch(selectedRecord.id, true)}
                className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700"
              >
                批准
              </button>
              <button
                onClick={() => handleApproveDispatch(selectedRecord.id, false, '不符合调度要求')}
                className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700"
              >
                拒绝
              </button>
              <button
                onClick={() => setShowApprovalModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 风险分析模态框 */}
      {showRiskModal && riskAnalysis && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <div className="flex items-center mb-4">
              <Icons.ExclamationTriangle className="w-6 h-6 text-yellow-500 mr-2" />
              <h3 className="text-lg font-semibold">风险分析</h3>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <p className="text-sm text-yellow-800">{riskAnalysis}</p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowRiskModal(false)}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
              >
                我已了解
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DispatchCenterPage;