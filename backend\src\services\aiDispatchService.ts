import { Employee, PowerStation } from '../models';
import { RiskAnalysis, RiskLevel } from '../models/DispatchRecord';
import axios from 'axios';
import { config } from '../config';

// AI调度服务接口
export interface AIQueryResult {
  sql: string;
  explanation: string;
  confidence: number;
}

export interface DispatchRecommendation {
  employeeId: string;
  fromStationId: string;
  toStationId: string;
  reason: string;
  confidence: number;
  estimatedDuration: number; // 天数
}

export interface RiskAnalysisInput {
  employeeId: string;
  fromStationId: string;
  toStationId: string;
  position: string;
}

// 阿里云百炼大模型调用函数
const callQwenAPI = async (messages: any[], model: string = 'qwen-plus'): Promise<string> => {
  const apiKey = config.dashscope.apiKey;
  const baseUrl = config.dashscope.baseUrl;

  if (!apiKey) {
    throw new Error('DASHSCOPE_API_KEY 未配置');
  }

  try {
    const response = await axios.post(
      `${baseUrl}/services/aigc/text-generation/generation`,
      {
        model: model,
        input: {
          messages: messages
        },
        parameters: {
          temperature: 0.1,
          top_p: 0.8,
          max_tokens: 2000,
          result_format: 'message'
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'X-DashScope-SSE': 'disable'
        },
        timeout: 30000
      }
    );

    if (response.data.output && response.data.output.choices && response.data.output.choices.length > 0) {
      return response.data.output.choices[0].message.content;
    } else {
      throw new Error('AI响应格式错误');
    }
  } catch (error: any) {
    console.error('调用百炼大模型失败:', error.response?.data || error.message);
    throw new Error(`AI服务调用失败: ${error.response?.data?.message || error.message}`);
  }
};

// 生成调度查询（使用阿里云百炼大模型）
export const generateDispatchQuery = async (naturalLanguageQuery: string): Promise<{
  mongoQuery: any;
  explanation: string;
  confidence: number;
}> => {
  try {
    console.log('🤖 使用百炼大模型解析查询:', naturalLanguageQuery);

    // 调用百炼大模型生成MongoDB查询
    const systemPrompt = `你是一个专业的数据库查询助手，专门将自然语言转换为MongoDB查询条件。

数据库结构：
1. employees 集合：
   - name: 员工姓名 (String)
   - position: 职位 (String) - 如"项目经理"、"电气专工"、"值长"、"安全员"等
   - department: 部门 (String) - 如"management"、"operations"、"maintenance"、"security"、"technical"
   - employeeType: 员工类型 (String) - "full_time"、"part_time"、"intern"、"external"、"borrowed"
   - status: 状态 (String) - "active"、"learning"、"inactive"、"vacant"
   - experience: 工作经验年限 (Number)
   - skills: 技能数组 (Array[String])
   - certifications: 证书数组 (Array[String])

请将用户的自然语言查询转换为MongoDB查询条件，返回JSON格式：
{
  "query": MongoDB查询对象,
  "explanation": "查询解释",
  "confidence": 置信度(0-1)
}`;

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: `请将以下查询转换为MongoDB查询条件：${naturalLanguageQuery}` }
    ];

    const response = await callQwenAPI(messages);

    // 尝试解析JSON响应
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const result = JSON.parse(jsonMatch[0]);
      console.log('✅ 百炼大模型解析结果:', result);
      return {
        mongoQuery: result.query || {},
        explanation: result.explanation || '查询解析完成',
        confidence: result.confidence || 0.8
      };
    } else {
      throw new Error('AI响应格式不正确');
    }
  } catch (error) {
    console.error('❌ 百炼大模型调用失败，使用后备方案:', error);

    // 后备方案：简单关键词匹配
    return generateFallbackQuery(naturalLanguageQuery);
  }
};

// 后备查询生成方案
const generateFallbackQuery = (naturalLanguageQuery: string) => {
  const keywords = naturalLanguageQuery.toLowerCase();
  const conditions: any = {};

  // 职位匹配
  if (keywords.includes('电工') || keywords.includes('电气')) {
    conditions.position = { $regex: '电', $options: 'i' };
  }
  if (keywords.includes('经理') || keywords.includes('管理')) {
    conditions.position = { $regex: '经理', $options: 'i' };
  }
  if (keywords.includes('值长')) {
    conditions.position = { $regex: '值长', $options: 'i' };
  }
  if (keywords.includes('安全')) {
    conditions.position = { $regex: '安全', $options: 'i' };
  }

  // 经验匹配
  const experienceMatch = keywords.match(/(\d+).*?年/);
  if (experienceMatch) {
    conditions.experience = { $gte: parseInt(experienceMatch[1]) };
  }

  // 状态匹配
  if (keywords.includes('在职') || keywords.includes('活跃')) {
    conditions.status = 'active';
  }

  return {
    mongoQuery: conditions,
    explanation: '基于关键词匹配生成的查询条件（后备方案）',
    confidence: 0.6
  };
};

// 分析调度风险
export const analyzeDispatchRisk = async (input: RiskAnalysisInput): Promise<RiskAnalysis> => {
  try {
    const { employeeId, fromStationId, toStationId, position } = input;
    
    // 获取调出电站的同岗位员工情况
    const fromStationEmployees = await Employee.find({
      currentStationId: fromStationId,
      position: position,
      status: 'active'
    });
    
    // 计算调度后剩余人员
    const remainingTotal = fromStationEmployees.length - 1;
    const remainingInterns = fromStationEmployees.filter(emp => 
      emp.employeeType === 'intern' && (emp as any)._id.toString() !== employeeId
    ).length;
    const remainingExperienced = fromStationEmployees.filter(emp => 
      emp.employeeType === 'full_time' && (emp as any)._id.toString() !== employeeId
    ).length;
    
    // 风险评估逻辑
    let riskLevel: RiskLevel = RiskLevel.LOW;
    let description = '';
    const recommendations: string[] = [];
    
    // 如果调度后该岗位无人
    if (remainingTotal === 0) {
      riskLevel = RiskLevel.CRITICAL;
      description = `调度后${position}岗位将无人值守，存在严重安全风险`;
      recommendations.push('立即安排其他电站人员临时支援');
      recommendations.push('考虑推迟调度或寻找替代人员');
    }
    // 如果只剩实习生
    else if (remainingExperienced === 0 && remainingInterns > 0) {
      riskLevel = RiskLevel.HIGH;
      description = `调度后${position}岗位仅剩${remainingInterns}名实习生，缺乏经验丰富的员工指导`;
      recommendations.push('安排经验丰富的员工进行远程指导');
      recommendations.push('考虑延长实习生培训期');
      recommendations.push('增加安全检查频次');
    }
    // 如果人员紧张
    else if (remainingTotal <= 2) {
      riskLevel = RiskLevel.MEDIUM;
      description = `调度后${position}岗位人员紧张，仅剩${remainingTotal}人`;
      recommendations.push('关注剩余人员工作负荷');
      recommendations.push('准备应急人员支援方案');
    }
    else {
      riskLevel = RiskLevel.LOW;
      description = `调度后${position}岗位人员充足，风险较低`;
      recommendations.push('正常执行调度计划');
    }
    
    // 获取受影响的岗位
    const affectedPositions = [position];
    
    return {
      level: riskLevel,
      description,
      affectedPositions,
      remainingStaff: {
        total: remainingTotal,
        interns: remainingInterns,
        experienced: remainingExperienced
      },
      recommendations
    };
  } catch (error) {
    console.error('风险分析失败:', error);
    return {
      level: RiskLevel.MEDIUM,
      description: '风险分析失败，建议人工评估',
      affectedPositions: [input.position],
      remainingStaff: {
        total: 0,
        interns: 0,
        experienced: 0
      },
      recommendations: ['请人工评估调度风险']
    };
  }
};

// 获取AI智能推荐（使用阿里云百炼大模型）
export const getAIRecommendations = async (requirement: string): Promise<{
  recommendations: DispatchRecommendation[];
  analysis: string;
  riskAssessment: string;
}> => {
  try {
    console.log('🤖 使用百炼大模型生成调度推荐:', requirement);

    // 获取所有电站和员工数据
    const [stations, employees] = await Promise.all([
      PowerStation.find({ status: 'active' }),
      Employee.find({ status: 'active' }).populate('homeStationId').populate('currentStationId')
    ]);

    // 调用百炼大模型生成推荐
    const systemPrompt = `你是一个专业的人员调度顾问，根据需求和可用人员数据生成最优的调度推荐。

请分析以下信息并生成调度推荐：
1. 考虑员工的技能匹配度
2. 考虑员工的经验水平
3. 考虑电站的地理位置和类型
4. 评估调度风险
5. 提供详细的推荐理由

返回JSON格式：
{
  "recommendations": [
    {
      "employeeId": "员工ID",
      "employeeName": "员工姓名",
      "fromStation": "来源电站",
      "toStation": "目标电站",
      "matchScore": 匹配度(0-100),
      "reason": "推荐理由",
      "riskLevel": "风险等级(low/medium/high)"
    }
  ],
  "analysis": "整体分析",
  "riskAssessment": "风险评估"
}`;

    const messages = [
      { role: 'system', content: systemPrompt },
      {
        role: 'user',
        content: `需求：${requirement}\n\n可用员工：${JSON.stringify(employees.slice(0, 10))}\n\n电站信息：${JSON.stringify(stations)}`
      }
    ];

    const response = await callQwenAPI(messages);

    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const aiResult = JSON.parse(jsonMatch[0]);
      console.log('✅ 百炼大模型推荐结果:', aiResult);

      // 转换AI结果为标准格式
      const recommendations: DispatchRecommendation[] = (aiResult.recommendations || []).map((rec: any) => ({
        employeeId: rec.employeeId,
        fromStationId: rec.fromStation,
        toStationId: rec.toStation,
        reason: rec.reason,
        confidence: rec.matchScore / 100,
        estimatedDuration: 7 // 默认7天
      }));

      return {
        recommendations,
        analysis: aiResult.analysis || '分析完成',
        riskAssessment: aiResult.riskAssessment || '风险评估完成'
      };
    } else {
      throw new Error('AI响应格式不正确');
    }
  } catch (error) {
    console.error('❌ 百炼大模型调用失败，使用后备方案:', error);

    // 后备方案：简单匹配算法
    return await getFallbackRecommendations(requirement);
  }
};

// 后备推荐方案
const getFallbackRecommendations = async (requirement: string) => {
  try {
    const keywords = requirement.toLowerCase();
    const recommendations: DispatchRecommendation[] = [];

    // 获取所有电站和员工数据
    const [stations, employees] = await Promise.all([
      PowerStation.find({ status: 'active' }),
      Employee.find({ status: 'active' }).populate('homeStationId').populate('currentStationId')
    ]);
    
    // 简单的推荐逻辑示例
    if (keywords.includes('电工') || keywords.includes('电气维护')) {
      const electricians = employees.filter(emp => 
        emp.position.includes('电工') && emp.employeeType === 'full_time'
      );
      
      // 推荐经验丰富的电工进行调度
      for (const electrician of electricians.slice(0, 3)) {
        const otherStations = stations.filter(station => 
          (station as any)._id.toString() !== (electrician.currentStationId as any)._id.toString()
        );
        
        if (otherStations.length > 0) {
          recommendations.push({
            employeeId: (electrician as any)._id.toString(),
            fromStationId: (electrician.currentStationId as any)._id.toString(),
            toStationId: (otherStations[0] as any)._id.toString(),
            reason: `${electrician.name}具有丰富的电气维护经验，适合支援其他电站`,
            confidence: 0.85,
            estimatedDuration: 7
          });
        }
      }
    }
    
    if (keywords.includes('紧急') || keywords.includes('应急')) {
      // 推荐就近电站的应急人员
      const emergencyStaff = employees.filter(emp => 
        emp.employeeType === 'full_time' && emp.status === 'active'
      );
      
      for (const staff of emergencyStaff.slice(0, 2)) {
        const nearbyStations = stations.filter(station => 
          (station as any)._id.toString() !== (staff.currentStationId as any)._id.toString()
        );
        
        if (nearbyStations.length > 0) {
          recommendations.push({
            employeeId: (staff as any)._id.toString(),
            fromStationId: (staff.currentStationId as any)._id.toString(),
            toStationId: (nearbyStations[0] as any)._id.toString(),
            reason: `紧急情况下，${staff.name}可快速响应支援`,
            confidence: 0.75,
            estimatedDuration: 3
          });
        }
      }
    }
    
    if (keywords.includes('培训') || keywords.includes('学习')) {
      // 推荐实习生到经验丰富的电站学习
      const interns = employees.filter(emp => emp.employeeType === 'intern');
      const experiencedStations = stations.filter(station => {
        const stationEmployees = employees.filter(emp => 
          (emp.currentStationId as any)._id.toString() === (station as any)._id.toString() && 
          emp.employeeType === 'full_time'
        );
        return stationEmployees.length >= 3; // 有足够经验丰富的员工
      });
      
      for (const intern of interns.slice(0, 2)) {
        if (experiencedStations.length > 0) {
          recommendations.push({
            employeeId: (intern as any)._id.toString(),
            fromStationId: (intern.currentStationId as any)._id.toString(),
            toStationId: (experiencedStations[0] as any)._id.toString(),
            reason: `安排${intern.name}到经验丰富的电站进行实践学习`,
            confidence: 0.80,
            estimatedDuration: 14
          });
        }
      }
    }
    
    return {
      recommendations,
      analysis: '基于简单匹配算法的推荐结果（后备方案）',
      riskAssessment: '中等风险，建议进一步人工评估'
    };
  } catch (error) {
    console.error('获取AI推荐失败:', error);
    return {
      recommendations: [],
      analysis: '推荐生成失败',
      riskAssessment: '无法评估风险'
    };
  }
};