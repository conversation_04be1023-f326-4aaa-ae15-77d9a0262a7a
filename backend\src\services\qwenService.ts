import axios from 'axios';
import { config } from '../config';

// 阿里云百炼大模型服务
export class QwenService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = config.dashscope.apiKey;
    this.baseUrl = config.dashscope.baseUrl;
    
    if (!this.apiKey) {
      console.warn('⚠️ DASHSCOPE_API_KEY 未配置，将使用模拟数据');
    }
  }

  /**
   * 调用阿里云百炼大模型
   */
  private async callQwen(messages: any[], model: string = 'qwen-plus'): Promise<string> {
    if (!this.apiKey) {
      throw new Error('DASHSCOPE_API_KEY 未配置');
    }

    try {
      const response = await axios.post(
        `${this.baseUrl}/services/aigc/text-generation/generation`,
        {
          model: model,
          input: {
            messages: messages
          },
          parameters: {
            temperature: 0.1,
            top_p: 0.8,
            max_tokens: 2000,
            result_format: 'message'
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'X-DashScope-SSE': 'disable'
          },
          timeout: 30000
        }
      );

      if (response.data.output && response.data.output.choices && response.data.output.choices.length > 0) {
        return response.data.output.choices[0].message.content;
      } else {
        throw new Error('AI响应格式错误');
      }
    } catch (error: any) {
      console.error('调用百炼大模型失败:', error.response?.data || error.message);
      throw new Error(`AI服务调用失败: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * 将自然语言查询转换为MongoDB查询条件
   */
  async generateMongoQuery(naturalLanguageQuery: string): Promise<{
    query: any;
    explanation: string;
    confidence: number;
  }> {
    const systemPrompt = `你是一个专业的数据库查询助手，专门将自然语言转换为MongoDB查询条件。

数据库结构：
1. employees 集合：
   - name: 员工姓名 (String)
   - position: 职位 (String) - 如"项目经理"、"电气专工"、"值长"、"安全员"等
   - department: 部门 (String) - 如"management"、"operations"、"maintenance"、"security"、"technical"
   - employeeType: 员工类型 (String) - "full_time"、"part_time"、"intern"、"external"、"borrowed"
   - status: 状态 (String) - "active"、"learning"、"inactive"、"vacant"
   - experience: 工作经验年限 (Number)
   - skills: 技能数组 (Array[String])
   - certifications: 证书数组 (Array[String])
   - homeStationId: 所属电站ID (ObjectId)
   - currentStationId: 当前电站ID (ObjectId)

2. powerstations 集合：
   - name: 电站名称 (String) - 如"青海中控50MW"、"青海中控10MW"等
   - type: 电站类型 (String) - "solar"、"wind"、"hydro"、"thermal"
   - capacity: 装机容量 (Number)
   - status: 状态 (String) - "active"、"maintenance"、"offline"

请将用户的自然语言查询转换为MongoDB查询条件，返回JSON格式：
{
  "query": MongoDB查询对象,
  "explanation": "查询解释",
  "confidence": 置信度(0-1)
}

注意：
1. 使用$regex进行模糊匹配
2. 使用$gte、$lte进行数值比较
3. 使用$in进行多值匹配
4. 对于电站相关查询，需要考虑关联查询`;

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: `请将以下查询转换为MongoDB查询条件：${naturalLanguageQuery}` }
    ];

    try {
      const response = await this.callQwen(messages);
      
      // 尝试解析JSON响应
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const result = JSON.parse(jsonMatch[0]);
        return {
          query: result.query || {},
          explanation: result.explanation || '查询解析完成',
          confidence: result.confidence || 0.8
        };
      } else {
        throw new Error('AI响应格式不正确');
      }
    } catch (error) {
      console.error('生成MongoDB查询失败:', error);
      // 返回简单的关键词匹配作为后备
      return this.generateFallbackQuery(naturalLanguageQuery);
    }
  }

  /**
   * 生成调度推荐
   */
  async generateDispatchRecommendation(requirement: string, employees: any[], stations: any[]): Promise<{
    recommendations: any[];
    analysis: string;
    riskAssessment: string;
  }> {
    const systemPrompt = `你是一个专业的人员调度顾问，根据需求和可用人员数据生成最优的调度推荐。

请分析以下信息并生成调度推荐：
1. 考虑员工的技能匹配度
2. 考虑员工的经验水平
3. 考虑电站的地理位置和类型
4. 评估调度风险
5. 提供详细的推荐理由

返回JSON格式：
{
  "recommendations": [
    {
      "employeeId": "员工ID",
      "employeeName": "员工姓名",
      "fromStation": "来源电站",
      "toStation": "目标电站",
      "matchScore": 匹配度(0-100),
      "reason": "推荐理由",
      "riskLevel": "风险等级(low/medium/high)"
    }
  ],
  "analysis": "整体分析",
  "riskAssessment": "风险评估"
}`;

    const messages = [
      { role: 'system', content: systemPrompt },
      { 
        role: 'user', 
        content: `需求：${requirement}\n\n可用员工：${JSON.stringify(employees.slice(0, 10))}\n\n电站信息：${JSON.stringify(stations)}` 
      }
    ];

    try {
      const response = await this.callQwen(messages);
      
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const result = JSON.parse(jsonMatch[0]);
        return {
          recommendations: result.recommendations || [],
          analysis: result.analysis || '分析完成',
          riskAssessment: result.riskAssessment || '风险评估完成'
        };
      } else {
        throw new Error('AI响应格式不正确');
      }
    } catch (error) {
      console.error('生成调度推荐失败:', error);
      return this.generateFallbackRecommendation(requirement, employees);
    }
  }

  /**
   * 后备查询生成（关键词匹配）
   */
  private generateFallbackQuery(query: string): any {
    const keywords = query.toLowerCase();
    const conditions: any = {};

    // 职位匹配
    if (keywords.includes('电工') || keywords.includes('电气')) {
      conditions.position = { $regex: '电', $options: 'i' };
    }
    if (keywords.includes('经理') || keywords.includes('管理')) {
      conditions.position = { $regex: '经理', $options: 'i' };
    }
    if (keywords.includes('值长')) {
      conditions.position = { $regex: '值长', $options: 'i' };
    }

    // 经验匹配
    const experienceMatch = keywords.match(/(\d+).*?年/);
    if (experienceMatch) {
      conditions.experience = { $gte: parseInt(experienceMatch[1]) };
    }

    // 状态匹配
    if (keywords.includes('在职') || keywords.includes('活跃')) {
      conditions.status = 'active';
    }

    return {
      query: conditions,
      explanation: '基于关键词匹配生成的查询条件',
      confidence: 0.6
    };
  }

  /**
   * 后备推荐生成
   */
  private generateFallbackRecommendation(requirement: string, employees: any[]): any {
    const recommendations = employees.slice(0, 3).map((emp, index) => ({
      employeeId: emp._id,
      employeeName: emp.name,
      fromStation: emp.currentStation?.name || '未知',
      toStation: '目标电站',
      matchScore: 80 - index * 10,
      reason: '基于基础匹配算法推荐',
      riskLevel: 'medium'
    }));

    return {
      recommendations,
      analysis: '基于简单匹配算法的推荐结果',
      riskAssessment: '中等风险，建议进一步评估'
    };
  }
}

export const qwenService = new QwenService();
