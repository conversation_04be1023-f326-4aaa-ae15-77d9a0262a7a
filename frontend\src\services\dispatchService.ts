import { apiClient } from './api';

export interface DispatchRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  fromStation: string;
  toStation: string;
  type: 'temporary' | 'permanent' | 'emergency';
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  source: 'manual' | 'ai_query' | 'ai_recommendation';
  requestDate: string;
  startDate: string;
  endDate?: string;
  reason: string;
  aiQuery?: string;
  riskAnalysis?: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectionReason?: string;
}

export interface CreateDispatchRequest {
  employeeId: string;
  fromStation: string;
  toStation: string;
  type: 'temporary' | 'permanent' | 'emergency';
  startDate: string;
  endDate?: string;
  reason: string;
  source?: 'manual' | 'ai_query' | 'ai_recommendation';
  aiQuery?: string;
}

export interface CreateBatchDispatchRequest {
  records: {
    employeeId: string;
    fromStationId: string;
    toStationId: string;
    dispatchType: string;
    startDate: string;
    endDate?: string;
  }[];
  reason: string;
  source?: 'manual' | 'ai_query' | 'ai_recommendation';
  aiQuery?: string;
}

export interface DispatchRecordsResponse {
  records: DispatchRecord[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    pages: number;
  };
}

export interface DispatchStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  completed: number;
  byType: Record<string, number>;
  bySource: Record<string, number>;
}

export interface BatchDispatchRequest {
  employeeIds: string[];
  fromStation?: string;
  toStation: string;
  type: 'temporary' | 'permanent' | 'emergency';
  startDate: string;
  endDate?: string;
  reason: string;
  source?: 'manual';
}

export interface AIQueryRequest {
  query: string;
}

export interface AIQueryResponse {
  employees: any[];
  riskAnalysis: string;
  sqlQuery?: string;
}

export interface AIRecommendationRequest {
  requirement: string;
}

export interface AIRecommendationResponse {
  recommendations: any[];
  riskAnalysis: string;
  reasoning?: string;
}

export interface ApprovalRequest {
  approved: boolean;
  reason?: string;
}

export class DispatchService {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3000/api/v1') {
    this.baseUrl = baseUrl;
  }

  // 获取调度记录
  async getDispatchRecords(params?: {
    status?: string;
    fromStationId?: string;
    toStationId?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    source?: string;
  }): Promise<DispatchRecordsResponse> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/dispatch/records${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.request(endpoint, { method: 'GET' });

    // 转换后端数据结构为前端期望的格式
    if (response?.data?.records) {
      const transformedRecords = response.data.records.map((record: any) => ({
        id: record.id || record._id,
        employeeId: record.employeeId,
        employeeName: record.employee?.name || '未知员工',
        fromStation: record.fromStation?.name || '未知电站',
        toStation: record.toStation?.name || '未知电站',
        type: record.dispatchType,
        status: record.status,
        source: record.source,
        requestDate: new Date(record.createdAt).toLocaleDateString(),
        startDate: new Date(record.startDate).toLocaleDateString(),
        endDate: record.endDate ? new Date(record.endDate).toLocaleDateString() : undefined,
        reason: record.reason,
        aiQuery: record.aiQuery,
        riskAnalysis: record.riskAnalysis?.description,
        approvedBy: record.reviewer?.username,
        approvedAt: record.reviewDate ? new Date(record.reviewDate).toLocaleDateString() : undefined,
        rejectionReason: record.reviewComments
      }));

      return {
        records: transformedRecords,
        pagination: response.data.pagination
      };
    }

    return { records: [], pagination: { current: 1, pageSize: 20, total: 0, pages: 0 } };
  }

  // 创建调度记录
  async createDispatch(request: CreateDispatchRequest): Promise<DispatchRecord> {
    return await apiClient.request('/dispatch/records', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 批量创建调度记录
  async createBatchDispatch(request: CreateBatchDispatchRequest): Promise<DispatchRecord[]> {
    return await apiClient.request('/dispatch/records/batch', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // AI查询员工
  async aiQuery(request: AIQueryRequest): Promise<AIQueryResponse> {
    return await apiClient.request('/dispatch/ai/query', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 获取AI调度推荐
  async aiRecommendation(request: AIRecommendationRequest): Promise<AIRecommendationResponse> {
    return await apiClient.request('/dispatch/ai/recommend', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 审核调度申请
  async approveDispatch(id: string, request: ApprovalRequest): Promise<DispatchRecord> {
    return await apiClient.request(`/dispatch/records/${id}/approve`, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 获取调度统计
  async getDispatchStats(): Promise<DispatchStats> {
    return await apiClient.request('/dispatch/stats', { method: 'GET' });
  }

  // 获取单个调度记录
  async getDispatchRecord(id: string): Promise<DispatchRecord> {
    return await apiClient.request(`/dispatch/records/${id}`, { method: 'GET' });
  }

  // 更新调度记录
  async updateDispatch(id: string, request: Partial<CreateDispatchRequest>): Promise<DispatchRecord> {
    return await apiClient.request(`/dispatch/records/${id}`, {
      method: 'PUT',
      body: JSON.stringify(request),
    });
  }

  // 删除调度记录
  async deleteDispatch(id: string): Promise<void> {
    await apiClient.request(`/dispatch/records/${id}`, { method: 'DELETE' });
  }
}

export const dispatchService = new DispatchService();