import React from 'react';
import { PowerStation, Employee, WorkOrder, UserRole, EmploymentStatus, EmployeeType, WorkOrderStatus } from '../types/index';

// SVG Icons
export const Icons = {
  Dashboard: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>
  ),
  Users: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M15 21a6 6 0 00-9-5.197m0 0A5.975 5.975 0 0112 13a5.975 5.975 0 013-1.197" /></svg>
  ),
  Calendar: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
  ),
  UserCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
  ),
  Refresh: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h5M20 20v-5h-5M4 4l1.5 1.5A9 9 0 0120.5 15M20 20l-1.5-1.5A9 9 0 003.5 9" /></svg>
  ),
  Cog: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
  ),
  Expand: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4h4M20 8V4h-4M4 16v4h4M20 16v4h-4" /></svg>
  ),
  Briefcase: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
  ),
  Search: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
  ),
  Lightning: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>
  ),
  Globe: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2h10a2 2 0 002-2v-1a2 2 0 012-2h1.945M7.704 4.343a9.003 9.003 0 0110.592 0m-12.58 7.314a9.003 9.003 0 0114.576 0M12 21a9.003 9.003 0 01-9-9" /></svg>
  ),
  ChartBar: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>
  ),
  ChartLine: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4-4 4-4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" /></svg>
  ),
   Leaf: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>
  ),
   Plus: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>
  ),
   Upload: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" /></svg>
  ),
  Edit: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" /></svg>
  ),
  Trash: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.134-2.033-2.134H8.033C6.91 2.75 6 3.704 6 4.834v.916m7.5 0" /></svg>
  ),
  Sparkles: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM18 13.5a3.375 3.375 0 00-3.375-3.375L12.75 9.75 12 12l.75 2.25 1.875.375a3.375 3.375 0 003.375-3.375z" /></svg>
  ),
  ChevronDown: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" /></svg>
  ),
  TrendingUp: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" /></svg>
  ),
  ExclamationTriangle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg>
  ),
  MessageSquare: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" /></svg>
  ),
  Bot: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c.232.232.348.694.348 1.154v.916a3.681 3.681 0 01-2.257 3.411c-.363.157-.59.518-.59.939 0 .423.69.684 1.103.684.609 0 1.103-.494 1.103-1.103a36.717 36.717 0 00-.831-5.784M19.8 15.3a40.717 40.717 0 00-.831 5.784 3.681 3.681 0 01-2.257 3.411c-.363.157-.59.518-.59.939 0 .423-.69.684-1.103.684-.609 0-1.103-.494-1.103-1.103a36.717 36.717 0 01.831-5.784m14.697.8a40.717 40.717 0 01-.831 5.784 3.681 3.681 0 002.257 3.411c.363.157.59.518.59.939 0 .423.69.684 1.103.684.609 0 1.103-.494 1.103-1.103a36.717 36.717 0 00-.831-5.784M5 14.5a40.717 40.717 0 01.831-5.784 3.681 3.681 0 012.257-3.411c.363-.157.59-.518.59-.939 0-.423-.69-.684-1.103-.684-.609 0-1.103.494-1.103 1.103a36.717 36.717 0 00.831 5.784M5 14.5l-1.402 1.402c-.232.232-.348.694-.348 1.154v.916a3.681 3.681 0 002.257 3.411c.363.157.59.518.59.939 0 .423-.69.684-1.103.684-.609 0-1.103-.494-1.103-1.103a36.717 36.717 0 01.831-5.784" /></svg>
  ),
  Send: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.768 59.768 0 013.27 20.876L5.999 12zm0 0h7.5" /></svg>
  ),
  XCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
  ),
  InformationCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" /></svg>
  ),
  CheckCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
  ),
  Bell: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" /></svg>
  ),
  Check: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" /></svg>
  ),
  Eye: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
  ),
  Activity: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z" /></svg>
  ),
  WifiOff: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M3 3l18 18M10.5 6.75a8.967 8.967 0 016.886 3.236M15.75 9.75a5.731 5.731 0 013.479 2.781m.771 4.257a8.967 8.967 0 01-2.209 2.961M9.736 9.736a5.002 5.002 0 012.514-.501m0 0V9a2.25 2.25 0 114.5 0v.75m-4.5 0h4.5m-4.5 0a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25h4.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25m-4.5 0V9a2.25 2.25 0 114.5 0v.75" /></svg>
  ),
  AlertTriangle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg>
  ),
};

export const SIDEBAR_NAV_ITEMS = [
  { name: '总览大屏', path: '/', icon: Icons.Dashboard, allowedRoles: [UserRole.ADMIN, UserRole.HR, UserRole.BUSINESS] },
  { name: '调度中心', path: '/dispatch', icon: Icons.Briefcase, allowedRoles: [UserRole.ADMIN, UserRole.HR, UserRole.BUSINESS] },
  { name: '运维管理', path: '/management', icon: Icons.Users, allowedRoles: [UserRole.ADMIN, UserRole.HR, UserRole.BUSINESS] },
];

const getResume = (name: string, position: string, experience: number) => `
### ${name || '未知员工'} - 个人简历

**基本信息**
- **职位:** ${position || '未分配职位'}
- **经验:** ${experience} 年
- **状态:** 在职

**专业技能**
- 熟练掌握光伏电站运维规程
- 精通高低压电气设备操作与维护
- 具备DCS/PLC系统监控和故障排除能力
- 熟悉安全生产规范，持有相关上岗证

**工作经历**
- **20${(new Date().getFullYear() - 2000) - experience}-至今:** 中光新能源 - ${position || '未分配职位'}
  - 负责场站设备的日常巡检、定期维护和故障处理。
  - 参与多次技术改造项目，有效提升了设备可靠性。
  - 编写和修订了多项设备操作和维护手册。

**自我评价**
本人工作认真负责，具备优秀的团队协作能力和问题解决能力。能够适应长期出差和艰苦环境下的工作。
`;

// Data parsed from the user-provided spreadsheet image
const stationData = [
  { id: 'S001', name: '青海中控50MW', address: '青海省', coords: { top: '45%', left: '35%' } },
  { id: 'S002', name: '青海中控10MW', address: '青海省', coords: { top: '46%', left: '38%' } },
  { id: 'S003', name: '格尔木西勘院', address: '青海省格尔木市', coords: { top: '44%', left: '30%' } },
  { id: 'S004', name: '吐鲁番浙火', address: '新疆吐鲁番市', coords: { top: '33%', left: '45%' } },
  { id: 'S005', name: '鄯善山东院', address: '新疆吐鲁番市鄯善县', coords: { top: '34%', left: '50%' } },
  { id: 'S006', name: '金塔中光', address: '甘肃省金塔县', coords: { top: '40%', left: '60%' } },
  { id: 'S007', name: '国能共和项目', address: '青海省共和县', coords: { top: '48%', left: '65%' } },
  { id: 'S008', name: '新疆博州项目', address: '新疆博尔塔拉蒙古自治州', coords: { top: '28%', left: '25%' } },
];

export const employeeRawData = [
// position, stationId, nameString, homeStationId (if different), status
// S1:青海中控50MW, S2:青海中控10MW, S3:格尔木, S4:吐鲁番, S5:鄯善, S6:金塔, S7:国能, S8:博州
['项目经理', 'S001', '杨俊奇'], ['项目经理', 'S003', '韩会珍'], ['项目经理', 'S004', '杨一啸'], ['项目经理', 'S005', '曹立军'], ['项目经理', 'S006', '韩会珍'], ['项目经理', 'S007', '王超'], ['项目经理', 'S008', '宋玉龙', null, 'PENDING'],
['项目副经理', 'S003', '蒋武桢（经理助理）'], ['项目副经理', 'S004', '宋玉龙'], ['项目副经理', 'S005', '李建伟'], ['项目副经理', 'S006', '李万隆'], ['项目副经理', 'S007', '冯程程'],
['生产部经理', 'S001', '贾立猛'], ['生产部经理', 'S003', '邓伟'], ['生产部经理', 'S004', '王学文'], ['生产部经理', 'S005', '孙宗利（兼）'], ['生产部经理', 'S006', '李积智'], ['生产部经理', 'S007', '王鸿文（兼）'],
['设备部经理', 'S001', '刘惠强'], ['设备部经理', 'S003', '于亮'], ['设备部经理', 'S004', '赵国龙'], ['设备部经理', 'S005', '薛召'], ['设备部经理', 'S006', '鲁永平'],
['安全专工', 'S001', '谢巍'], ['安全专工', 'S003', '孔瑞（陕投提供）'], ['安全专工', 'S004', '李育贤'], ['安全专工', 'S005', '杜良虎'], ['安全专工', 'S006', '马臻'],
['安全员', 'S002', '李鑫'], ['安全员', 'S003', '王志昌'], ['安全员', 'S005', '董维'], ['安全员', 'S006', '林生栋兼'], ['安全员', 'S008', '郑玉鹏', null, 'PENDING'],
['值长1', 'S002', '刘生杰'], ['值长1', 'S003', '邓子成'], ['值长1', 'S004', '宋秀亮'], ['值长1', 'S005', '王屿'], ['值长1', 'S006', '李鹏'], ['值长1', 'S007', '陈德鹏'], ['值长1', 'S008', '丁晓', null, 'PENDING'],
['值长2', 'S002', '张文强'], ['值长2', 'S003', '魏忠旺'], ['值长2', 'S004', '张碧林'], ['值长2', 'S005', '李仲宝'], ['值长2', 'S006', '赵琪'], ['值长2', 'S008', '王晋', null, 'PENDING'],
['值长3', 'S002', '沈玉光'], ['值长3', 'S003', '张占鑫'], ['值长3', 'S004', '王东华'], ['值长3', 'S005', '皮家顺（学习）'], ['值长3', 'S006', '吴雪南'],
['值长4', 'S001', '费翔（学习）'], ['值长4', 'S003', '刘国林（学习）'], ['值长4', 'S004', '张红义'], ['值长4', 'S005', '张富昌（学习）'], ['值长4', 'S006', '李林鹏'],
['值长5', 'S001', '赵得才（学习）'],
['值长6', 'S001', '旦木珍'],
['值长7', 'S001', '朱发维'],
['聚光集热专工', 'S001', '王生超'], ['聚光集热专工', 'S003', '邓伟兼'], ['聚光集热专工', 'S004', '王学文兼'], ['聚光集热专工', 'S005', '谢德胜'], ['聚光集热专工', 'S006', '吴雪南兼职'], ['聚光集热专工', 'S007', '王鸿文'],
['储换热专工', 'S001', '虞四化'], ['储换热专工', 'S003', '魏新忠'], ['储换热专工', 'S004', '张萌'], ['储换热专工', 'S005', '李仲宝（兼）'], ['储换热专工', 'S006', '李万成'], ['储换热专工', 'S007', '郭永寿'],
['电气专工', 'S001', '杨春生'], ['电气专工', 'S003', '蒋武桢兼'], ['电气专工', 'S004', '杨瑞斌'], ['电气专工', 'S005', '王鹏武（陕投）'], ['电气专工', 'S006', '张海鹏'], ['电气专工', 'S007', '雷郭（临时）'], ['电气专工', 'S008', '刘雷', null, 'PENDING'],
['热控/DCS专工', 'S001', '王正军'], ['热控/DCS专工', 'S002', '王玉凤'], ['热控/DCS专工', 'S003', '饶荣'], ['热控/DCS专工', 'S004', '乔文玲（陕投）李康'], ['热控/DCS专工', 'S005', '王月生'], ['热控/DCS专工', 'S006', '李红海'], ['热控/DCS专工', 'S007', '外委', null, 'CONTRACTOR'],
['化水专工', 'S001', '牛泉欢'], ['化水专工', 'S003', '刘国林兼'], ['化水专工', 'S004', '宋贵生(陕投)'], ['化水专工', 'S005', '马晓辉（陕投）'], ['化水专工', 'S006', '鲁文'], ['化水专工', 'S007', '刘秋锋'],
['镜场专工', 'S001', '李积才（兼）'], ['镜场专工', 'S004', '李积才'], ['镜场专工', 'S005', '李积才兼'], ['镜场专工', 'S006', '李积才兼'], ['镜场专工', 'S007', '李积才兼'],
['汽机专工', 'S001', '范本科（国能借调）王盛'], ['汽机专工', 'S003', '樊全伟'], ['汽机专工', 'S004', '李建峰'], ['汽机专工', 'S005', '孙宗利'], ['汽机专工', 'S006', '刘双保'], ['汽机专工', 'S007', '胡超勇（外委）', null, 'CONTRACTOR'],
['聚光集热主操1', 'S001', '张生发'], ['聚光集热主操1', 'S002', '罗长星'], ['聚光集热主操1', 'S003', '王兴源（学习）'], ['聚光集热主操1', 'S004', '余孝鹏（学习）'], ['聚光集热主操1', 'S005', '王宝财（学习）'], ['聚光集热主操1', 'S006', '徐文近'],
['聚光集热主操2', 'S001', '包生林'], ['聚光集热主操2', 'S002', '马耀林'], ['聚光集热主操2', 'S003', '戴学强（学习）'], ['聚光集热主操2', 'S004', '李志龙（学习）'], ['聚光集热主操2', 'S005', '吴正祥（学习）'], ['聚光集热主操2', 'S006', '赵生亮'],
['聚光集热主操3', 'S001', '薛心蛟（学习）'], ['聚光集热主操3', 'S002', '窦增通'], ['聚光集热主操3', 'S003', '左国良（学习）'], ['聚光集热主操3', 'S004', '荆鹏烨'], ['聚光集热主操3', 'S005', '马金宝（借调）'], ['聚光集热主操3', 'S006', '李玉德'],
['聚光集热主操4', 'S001', '谢世财'], ['聚光集热主操4', 'S003', '李存鹏（学习）'], ['聚光集热主操4', 'S004', '安晓康（学习）'], ['聚光集热主操4', 'S005', '李积鹏（学习）'], ['聚光集热主操4', 'S006', '陈永明'],
['储换热主操1', 'S001', '陈鹤林'], ['储换热主操1', 'S002', '李晓明'], ['储换热主操1', 'S003', '张国栋'], ['储换热主操1', 'S004', '杨昊年（学习）'], ['储换热主操1', 'S005', '石成义（学习）'], ['储换热主操1', 'S006', '陈天辉'], ['储换热主操1', 'S007', '童发光借调山东院', 'S005'], ['储换热主操1', 'S008', '杨志超', null, 'PENDING'],
['储换热主操2', 'S001', '张民庭'], ['储换热主操2', 'S002', '郑怀艳'], ['储换热主操2', 'S003', '童发光（国能借调）', 'S007'], ['储换热主操2', 'S004', '伊文沛'], ['储换热主操2', 'S005', '张金涛'], ['储换热主操2', 'S006', '李泽阳'], ['储换热主操2', 'S007', '文生福'], ['储换热主操2', 'S008', '赵晋宁', null, 'PENDING'],
['储换热主操3', 'S001', '陈希平'], ['储换热主操3', 'S002', '李翔'], ['储换热主操3', 'S004', '丁宜杰（学习）'], ['储换热主操3', 'S005', '牛鑫德（学习）'], ['储换热主操3', 'S006', '李长江（学习）'], ['储换热主操3', 'S007', '刘君琛'],
['储换热主操4', 'S001', '蒋崇山'], ['储换热主操4', 'S004', '赵强'], ['储换热主操4', 'S005', '赵世章（学习）'], ['储换热主操4', 'S006', '金玉国'],
['电气主操1', 'S001', '车军贤'], ['电气主操1', 'S002', '朱青林（学习）'], ['电气主操1', 'S003', '雷有隆'], ['电气主操1', 'S004', '翟升'], ['电气主操1', 'S005', '常通博'], ['电气主操1', 'S006', '陈伟'], ['电气主操1', 'S007', '罗永福借调格尔木', 'S003'], ['电气主操1', 'S008', '王生科', null, 'PENDING'],
['电气主操2', 'S001', '苏德顺/保广云（学习）'], ['电气主操2', 'S002', '刁顺发（学习）'], ['电气主操2', 'S003', '韩永民'], ['电气主操2', 'S004', '罗鹏'], ['电气主操2', 'S005', '陈瑞（陕投）'], ['电气主操2', 'S006', '郑鹏贤'], ['电气主操2', 'S007', '外委', null, 'CONTRACTOR'], ['电气主操2', 'S008', '李宝', null, 'PENDING'],
['电气主操3', 'S001', '颜鲁明'], ['电气主操3', 'S002', '冯延垚（学习）'], ['电气主操3', 'S003', '马田瑞'], ['电气主操3', 'S004', '邢鹏鸿'], ['电气主操3', 'S005', '王亮（陕投）'], ['电气主操3', 'S006', '祁永龙'], ['电气主操3', 'S007', '外委', null, 'CONTRACTOR'],
['电气主操4', 'S001', '张永旺（学习）'], ['电气主操4', 'S003', '罗永福（借调）李文'], ['电气主操4', 'S004', '苏玉凯'], ['电气主操4', 'S005', '李嘉龙（借调）'], ['电气主操4', 'S006', '马成伟'], ['电气主操4', 'S007', '石钟华'],
['汽机主操1', 'S001', '吴鲍昌'], ['汽机主操1', 'S002', '尚朝（学习）'], ['汽机主操1', 'S003', '外委', null, 'CONTRACTOR'], ['汽机主操1', 'S004', '陕投（高建军）'], ['汽机主操1', 'S005', '王伟（陕投）'], ['汽机主操1', 'S006', '董明源'], ['汽机主操1', 'S007', '李彦龙暂调格尔木', 'S003'], ['汽机主操1', 'S008', '李富鹏', null, 'PENDING'],
['汽机主操2', 'S001', '王占孝'], ['汽机主操2', 'S002', '张永旭'], ['汽机主操2', 'S003', '李彦龙（借调）'], ['汽机主操2', 'S004', '杨浩'], ['汽机主操2', 'S005', '程江（陕投）'], ['汽机主操2', 'S006', '外委', null, 'CONTRACTOR'], ['汽机主操2', 'S007', '外委', null, 'CONTRACTOR'], ['汽机主操2', 'S008', '裴佳辉', null, 'PENDING'],
['汽机主操3', 'S001', '李军'], ['汽机主操3', 'S002', '董继安'], ['汽机主操3', 'S003', '外委', null, 'CONTRACTOR'], ['汽机主操3', 'S004', '陕投（刘长命）'], ['汽机主操3', 'S005', '外委', null, 'CONTRACTOR'], ['汽机主操3', 'S006', '外委', null, 'CONTRACTOR'], ['汽机主操3', 'S007', '外委', null, 'CONTRACTOR'],
['汽机主操4', 'S001', '陈永文'], ['汽机主操4', 'S003', '张伟'], ['汽机主操4', 'S004', '闫星东（陕投）'], ['汽机主操4', 'S005', '外委', null, 'CONTRACTOR'], ['汽机主操4', 'S007', '外委或公司内部解决'],
['化水主操1', 'S001', '王玉凤1'], ['化水主操1', 'S002', '朵玲霞'], ['化水主操1', 'S003', '外委', null, 'CONTRACTOR'], ['化水主操1', 'S004', '马少雄（陕投）'], ['化水主操1', 'S005', '王超（陕投）'], ['化水主操1', 'S006', '蔡国俊'], ['化水主操1', 'S007', '外委', null, 'CONTRACTOR'], ['化水主操1', 'S008', '姜新兰', null, 'PENDING'],
['化水主操2', 'S001', '陈占红'], ['化水主操2', 'S002', '赵雯倩（实习）'], ['化水主操2', 'S004', '吕建华'], ['化水主操2', 'S005', '王强（实习生）'], ['化水主操2', 'S006', '外委', null, 'CONTRACTOR'], ['化水主操2', 'S007', '外委', null, 'CONTRACTOR'],
['化水主操3', 'S001', '孙佳琪'], ['化水主操3', 'S002', '王丽娟（实习）'], ['化水主操3', 'S004', '何盼来（陕投）'], ['化水主操3', 'S005', '崔成龙（实习生）'], ['化水主操3', 'S007', '外委或公司内部解决'],
['化水主操4', 'S001', '星全兰'], ['化水主操4', 'S003', '陕投'], ['化水主操4', 'S004', '吴宇飞（陕投）'], ['化水主操4', 'S007', '外委或公司内部解决'],
// And so on for all employees...
];


let employeeCounter = 1;
const allEmployees: Employee[] = employeeRawData.map(([position, currentStationId, nameString, customField, customStatus]) => {
    const id = `E${String(employeeCounter++).padStart(3, '0')}`;
    let name = nameString?.split('（')[0].split('/')[0].trim() || '未知员工';
    const isIntern = nameString?.includes('学习') || nameString?.includes('实习') || false;
    const status = customStatus === 'PENDING' ? EmploymentStatus.PENDING : EmploymentStatus.ON_DUTY;
    const employeeType = customStatus === 'CONTRACTOR' ? EmployeeType.CONTRACTOR : isIntern ? EmployeeType.INTERN : EmployeeType.PERMANENT;
    
    if (employeeType === EmployeeType.CONTRACTOR) name = `外委人员`;

    // Logic to determine home vs current station for dispatched employees
    let homeStationId = currentStationId || 'S001';
    let actualCurrentStationId = currentStationId || 'S001';

    if (typeof customField === 'string' && customField.startsWith('S')) {
      // This is a dispatched employee, the main station ID is their current one, custom field is home
      homeStationId = customField;
    } else if (nameString?.includes('借调')) {
        const match = nameString.match(/借调(.*?)$/);
        const homeStationNamePart = match ? match[1] : '';
        const homeStation = stationData.find(s => s.name.includes(homeStationNamePart));
        if (homeStation) {
            homeStationId = homeStation.id;
        }
    } else if (nameString?.includes('暂调')) {
        const match = nameString.match(/暂调(.*?)$/);
        const homeStationNamePart = match ? match[1] : '';
        const homeStation = stationData.find(s => s.name.includes(homeStationNamePart));
        if (homeStation) {
            homeStationId = homeStation.id;
        }
    }


    const getLevel = (pos: string) => {
        if (pos.includes('经理')) return 'M3';
        if (pos.includes('专工')) return 'P7';
        if (pos.includes('班长')) return 'P6';
        if (pos.includes('主操') || pos.includes('值长')) return 'P5';
        if (pos.includes('检修')) return 'P4';
        return 'P3';
    };

    return {
        id,
        name,
        position: position || '未分配职位',
        department: '运维部', // 添加部门信息
        employeeType,
        experience: Math.floor(Math.random() * 10) + (isIntern ? 0 : 1),
        currentStationId: actualCurrentStationId,
        homeStationId: homeStationId,
        status,
        level: getLevel(position || ''),
        hireDate: new Date(new Date().setFullYear(new Date().getFullYear() - Math.floor(Math.random() * 5))), // 添加入职日期
        resumeUrl: getResume(name, position || '未分配职位', 5),
        scheduleFrequency: Math.floor(Math.random() * 5),
        lastScheduled: new Date(new Date().setMonth(new Date().getMonth() - Math.floor(Math.random() * 12))),
        totalScheduledDuration: Math.floor(Math.random() * 200),
    };
});

// Use the full list of employees
export const MOCK_EMPLOYEES: Employee[] = allEmployees;

const stationStaffing = stationData.map(station => {
    const staffing: PowerStation['staffing'] = {};
    const rolesInStation = [...new Set(employeeRawData.filter(e => e[1] === station.id).map(e => e[0]))];
    
    rolesInStation.forEach(role => {
        if (role) { // 确保 role 不为 null
            const requiredCount = employeeRawData.filter(e => e[1] === station.id && e[0] === role).length;
            const currentCount = allEmployees.filter(e => e.currentStationId === station.id && e.position === role && e.employeeType !== EmployeeType.CONTRACTOR && e.status === EmploymentStatus.ON_DUTY).length;
            staffing[role] = { current: currentCount, required: requiredCount };
        }
    });
    
    return {
        ...station,
        staffing
    };
});

export const MOCK_STATIONS: PowerStation[] = stationStaffing.map(s => ({
    ...s,
    annualGeneration: Math.floor(Math.random() * 10000) + 5000,
    totalGeneration: Math.floor(Math.random() * 80000) + 20000,
}));


export const MOCK_WORK_ORDERS: WorkOrder[] = [
  { id: 'W001', title: '3号逆变器故障', description: '逆变器发出异响，功率输出不稳定。', stationId: 'S001', createdBy: '张伟', assignedTo: '李娜', status: WorkOrderStatus.IN_PROGRESS, createdAt: new Date('2024-06-18T10:00:00Z'), type: '报修'},
  { id: 'W002', title: '光伏板季度清洁', description: 'C区光伏板积灰严重，影响发电效率。', stationId: 'S002', createdBy: '系统', status: WorkOrderStatus.OPEN, createdAt: new Date('2024-06-20T09:00:00Z'), type: '定期维护'},
];