import { Request, Response } from 'express';
import DispatchRecord, { DispatchStatus, DispatchSource, RiskLevel, RiskAnalysis } from '../models/DispatchRecord';
import { Employee, PowerStation } from '../models';
import { generateDispatchQuery, analyzeDispatchRisk, getAIRecommendations } from '../services/aiDispatchService';
import mongoose from 'mongoose';

// 获取调度记录列表
export const getDispatchRecords = async (req: Request, res: Response) => {
  try {
    const { 
      status, 
      fromStationId, 
      toStationId, 
      startDate, 
      endDate,
      page = 1, 
      limit = 20,
      source 
    } = req.query;

    const filter: any = {};
    
    if (status) filter.status = status;
    if (fromStationId) filter.fromStationId = fromStationId;
    if (toStationId) filter.toStationId = toStationId;
    if (source) filter.source = source;
    
    if (startDate || endDate) {
      filter.startDate = {};
      if (startDate) filter.startDate.$gte = new Date(startDate as string);
      if (endDate) filter.startDate.$lte = new Date(endDate as string);
    }

    const skip = (Number(page) - 1) * Number(limit);
    
    const [records, total] = await Promise.all([
      DispatchRecord.find(filter)
        .populate('employee', 'name position department employeeType')
        .populate('fromStation', 'name')
        .populate('toStation', 'name')
        .populate('reviewer', 'username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      DispatchRecord.countDocuments(filter)
    ]);

    res.json({
      success: true,
      data: {
        records,
        pagination: {
          current: Number(page),
          pageSize: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('获取调度记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取调度记录失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 创建调度申请
export const createDispatchRecord = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      employeeId,
      fromStationId,
      toStationId,
      dispatchType,
      startDate,
      endDate,
      reason,
      source = DispatchSource.MANUAL,
      aiQuery,
      batchId,
      isBatch = false
    } = req.body;

    // 验证员工和电站是否存在
    const [employee, fromStation, toStation] = await Promise.all([
      Employee.findById(employeeId),
      PowerStation.findById(fromStationId),
      PowerStation.findById(toStationId)
    ]);

    if (!employee) {
      res.status(404).json({
        success: false,
        message: '员工不存在'
      });
      return;
    }

    if (!fromStation || !toStation) {
      res.status(404).json({
        success: false,
        message: '电站不存在'
      });
      return;
    }

    // 进行风险分析
    const riskAnalysis = await analyzeDispatchRisk({
      employeeId,
      fromStationId,
      toStationId,
      position: employee.position
    });

    const dispatchRecord = new DispatchRecord({
      employeeId,
      fromStationId,
      toStationId,
      dispatchType,
      startDate: new Date(startDate),
      endDate: endDate ? new Date(endDate) : undefined,
      reason,
      source,
      aiQuery,
      batchId,
      isBatch,
      riskAnalysis,
      status: DispatchStatus.PENDING
    });

    await dispatchRecord.save();
    
    // 填充关联数据
    await dispatchRecord.populate([
      { path: 'employee', select: 'name position department employeeType' },
      { path: 'fromStation', select: 'name' },
      { path: 'toStation', select: 'name' }
    ]);

    res.status(201).json({
      success: true,
      data: dispatchRecord,
      message: '调度申请创建成功'
    });
  } catch (error) {
    console.error('创建调度申请失败:', error);
    res.status(500).json({
      success: false,
      message: '创建调度申请失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 批量创建调度申请
export const createBatchDispatchRecords = async (req: Request, res: Response): Promise<void> => {
  try {
    const { records, reason, source = DispatchSource.MANUAL, aiQuery } = req.body;
    
    if (!Array.isArray(records) || records.length === 0) {
      res.status(400).json({
        success: false,
        message: '调度记录数组不能为空'
      });
      return;
    }

    const batchId = new mongoose.Types.ObjectId().toString();
    const createdRecords = [];

    for (const record of records) {
      const {
        employeeId,
        fromStationId,
        toStationId,
        dispatchType,
        startDate,
        endDate
      } = record;

      // 验证员工和电站
      const [employee, fromStation, toStation] = await Promise.all([
        Employee.findById(employeeId),
        PowerStation.findById(fromStationId),
        PowerStation.findById(toStationId)
      ]);

      if (!employee || !fromStation || !toStation) {
        continue; // 跳过无效记录
      }

      // 进行风险分析
      const riskAnalysis = await analyzeDispatchRisk({
        employeeId,
        fromStationId,
        toStationId,
        position: employee.position
      });

      const dispatchRecord = new DispatchRecord({
        employeeId,
        fromStationId,
        toStationId,
        dispatchType,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : undefined,
        reason,
        source,
        aiQuery,
        batchId,
        isBatch: true,
        riskAnalysis,
        status: DispatchStatus.PENDING
      });

      await dispatchRecord.save();
      createdRecords.push(dispatchRecord);
    }

    res.status(201).json({
      success: true,
      data: {
        batchId,
        count: createdRecords.length,
        records: createdRecords
      },
      message: `批量创建${createdRecords.length}条调度申请成功`
    });
  } catch (error) {
    console.error('批量创建调度申请失败:', error);
    res.status(500).json({
      success: false,
      message: '批量创建调度申请失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// AI查询员工
export const aiQueryEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { query } = req.body;
    
    if (!query || typeof query !== 'string') {
      res.status(400).json({
        success: false,
        message: '查询条件不能为空'
      });
      return;
    }

    // 使用百炼大模型生成MongoDB查询
    const queryResult = await generateDispatchQuery(query);

    console.log('🔍 AI生成的查询条件:', queryResult);

    // 执行MongoDB查询
    const employees = await Employee.find(queryResult.mongoQuery)
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name')
      .limit(50); // 限制结果数量

    // 生成风险分析
    const riskAnalysis = `AI查询解析：${queryResult.explanation}\n置信度：${(queryResult.confidence * 100).toFixed(1)}%\n查询到 ${employees.length} 名符合条件的员工`;

    res.json({
      success: true,
      query,
      mongoQuery: queryResult.mongoQuery,
      explanation: queryResult.explanation,
      confidence: queryResult.confidence,
      employees,
      count: employees.length,
      riskAnalysis
    });
  } catch (error) {
    console.error('AI查询员工失败:', error);
    res.status(500).json({
      success: false,
      message: 'AI查询员工失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// AI智能推荐
export const getAIDispatchRecommendations = async (req: Request, res: Response): Promise<void> => {
  try {
    const { requirement } = req.body;
    
    if (!requirement || typeof requirement !== 'string') {
      res.status(400).json({
        success: false,
        message: '需求描述不能为空'
      });
      return;
    }

    // 获取AI推荐
    const aiResult = await getAIRecommendations(requirement);

    res.json({
      success: true,
      requirement,
      recommendations: aiResult.recommendations,
      analysis: aiResult.analysis,
      riskAssessment: aiResult.riskAssessment,
      riskAnalysis: `${aiResult.analysis}\n\n风险评估：${aiResult.riskAssessment}`
    });
  } catch (error) {
    console.error('获取AI推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取AI推荐失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 审核调度申请
export const reviewDispatchRecord = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { action, comments } = req.body; // action: 'approve' | 'reject'
    const reviewerId = (req as any).user?.id; // 假设从认证中间件获取

    const dispatchRecord = await DispatchRecord.findById(id);
    
    if (!dispatchRecord) {
      res.status(404).json({
        success: false,
        message: '调度记录不存在'
      });
      return;
    }

    if (dispatchRecord.status !== DispatchStatus.PENDING) {
      res.status(400).json({
        success: false,
        message: '只能审核待审批的调度申请'
      });
      return;
    }

    const newStatus = action === 'approve' ? DispatchStatus.APPROVED : DispatchStatus.REJECTED;
    
    dispatchRecord.status = newStatus;
    dispatchRecord.reviewerId = reviewerId;
    dispatchRecord.reviewDate = new Date();
    dispatchRecord.reviewComments = comments;

    // 如果审批通过，更新员工的当前电站
    if (action === 'approve') {
      await Employee.findByIdAndUpdate(dispatchRecord.employeeId, {
        currentStationId: dispatchRecord.toStationId,
        status: 'dispatched'
      });
      
      // 将状态更新为执行中
      dispatchRecord.status = DispatchStatus.ACTIVE;
    }

    await dispatchRecord.save();

    await dispatchRecord.populate([
      { path: 'employee', select: 'name position department employeeType' },
      { path: 'fromStation', select: 'name' },
      { path: 'toStation', select: 'name' },
      { path: 'reviewer', select: 'username' }
    ]);

    res.json({
      success: true,
      data: dispatchRecord,
      message: action === 'approve' ? '调度申请审批通过' : '调度申请已拒绝'
    });
  } catch (error) {
    console.error('审核调度申请失败:', error);
    res.status(500).json({
      success: false,
      message: '审核调度申请失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 获取调度统计
export const getDispatchStatistics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { startDate, endDate } = req.query;
    
    const dateFilter: any = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate as string);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate as string);
    }

    const [
      totalRecords,
      pendingRecords,
      approvedRecords,
      activeRecords,
      completedRecords,
      rejectedRecords,
      bySource,
      byRiskLevel
    ] = await Promise.all([
      DispatchRecord.countDocuments(dateFilter),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.PENDING }),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.APPROVED }),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.ACTIVE }),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.COMPLETED }),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.REJECTED }),
      DispatchRecord.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$source', count: { $sum: 1 } } }
      ]),
      DispatchRecord.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$riskAnalysis.level', count: { $sum: 1 } } }
      ])
    ]);

    res.json({
      success: true,
      data: {
        total: totalRecords,
        byStatus: {
          pending: pendingRecords,
          approved: approvedRecords,
          active: activeRecords,
          completed: completedRecords,
          rejected: rejectedRecords
        },
        bySource: bySource.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byRiskLevel: byRiskLevel.reduce((acc, item) => {
          if (item._id) acc[item._id] = item.count;
          return acc;
        }, {})
      }
    });
  } catch (error) {
    console.error('获取调度统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取调度统计失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};